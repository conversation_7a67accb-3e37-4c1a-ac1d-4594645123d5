{"version": 3, "file": "AsyncAction.d.ts", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/AsyncAction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAGlD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,qBAAa,WAAW,CAAC,CAAC,CAAE,SAAQ,MAAM,CAAC,CAAC,CAAC;IAO/B,SAAS,CAAC,SAAS,EAAE,cAAc;IAAE,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI;IANvG,EAAE,EAAE,WAAW,GAAG,SAAS,CAAC;IAC5B,KAAK,CAAC,EAAE,CAAC,CAAC;IAEV,KAAK,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,OAAO,EAAE,OAAO,CAAS;gBAEb,SAAS,EAAE,cAAc,EAAY,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI;IAIvG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,GAAE,MAAU,GAAG,YAAY;IA+C3D,SAAS,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,GAAG,CAAC,EAAE,WAAW,EAAE,KAAK,GAAE,MAAU,GAAG,WAAW;IAItG,SAAS,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,WAAW,EAAE,KAAK,GAAE,MAAM,GAAG,IAAQ,GAAG,WAAW,GAAG,SAAS;IAczH;;;OAGG;IACI,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG;IA2B5C,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,GAAG,GAAG;IAkBjD,WAAW;CAiBZ"}